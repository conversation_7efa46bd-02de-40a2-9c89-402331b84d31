<template>
	<scroll-view 
		class="profile-container no-scrollbar" 
		scroll-y="true" 
		show-scrollbar="false"
		refresher-enabled="true"
		:refresher-triggered="isRefreshing"
		@refresherrefresh="onRefresh"
		@scrolltolower="loadMoreWorks">
		<!-- 顶部安全区域 -->
		<view class="safe-area"></view>
		
		<!-- 顶部用户信息卡片 -->
		<view class="user-card">
			<view class="avatar-container" @click="openProfileEdit">
				<image class="avatar" :src="userInfo.avatar_url ? getImageUrl(userInfo.avatar_url) : '/static/头像.png'"></image>
				<view class="avatar-edit-icon">
					<view class="icon-edit"></view>
				</view>
			</view>
			<view class="user-info" @click="openProfileEdit">
				<text class="username">{{ userInfo.nickname || '哇图AI' }}</text>
				<text class="user-id" @click.stop="copyUserId">ID: {{ userInfo.uuid || '' }}</text>
			</view>
			<view class="coin-info">
				<view class="coin-balance" @click="openRecharge">
					<image class="coin-icon" src="/static/coins.png"></image>
					<text class="coin-amount">{{ userInfo.coins || 0 }}</text>
				</view>
				<view class="recharge-btn" @click="openRecharge">获取</view>
			</view>
		</view>
		
		<!-- 快捷功能卡片区 -->
		<view class="quick-tools">
			<view v-for="(item, index) in quickTools" :key="index" :class="{'tool-item': true, 'service-tool': item.action === 'service'}">
				<!-- 客服按钮使用button组件 -->
				<button v-if="item.action === 'service'" class="service-button" open-type="contact">
					<view class="tool-icon-wrapper">
						<view class="tool-icon" :class="item.iconClass"></view>
					</view>
					<view class="tool-text-container">
						<text class="tool-text">{{item.title}}</text>
					</view>
				</button>
				
				<!-- 其他按钮保持原样 -->
				<view v-else class="tool-content" @click="handleToolClick(item.action)">
					<view class="tool-icon-wrapper">
						<view class="tool-icon" :class="item.iconClass"></view>
						<!-- 如果是签到按钮且未签到，则显示提醒红点 -->
						<view v-if="item.action === 'signIn' && !isSignedInToday" class="notification-dot"></view>
					</view>
					<view class="tool-text-container">
						<!-- 签到按钮根据签到状态显示不同文字 -->
						<text v-if="item.action === 'signIn' && !isSignedInToday" class="tool-text">每日签到</text>
						<text v-else-if="item.action === 'signIn' && isSignedInToday" class="signed-tag">已签到</text>
						<!-- 其他按钮正常显示 -->
						<text v-else class="tool-text">{{item.title}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 我的作品区域 -->
		<view class="works-section">
			<view class="section-title">我的作品</view>
			
			<!-- 加载中状态 -->
			<view v-if="isLoadingWorks && myGenerations.length === 0" class="loading-works">
				<view class="loading-indicator">
					<view class="loading-spinner"></view>
					<text class="loading-text">加载作品中...</text>
				</view>
			</view>
			
			<!-- 无作品状态 -->
			<view v-else-if="myGenerations.length === 0" class="no-works">
				<text>暂无作品，快去生成吧！</text>
				<view class="empty-btn" @click="navigateToGenerate">创建新作品</view>
			</view>
			
			<!-- 作品列表 -->
			<view v-else class="works-grid">
				<view class="work-item" v-for="(gen, index) in myGenerations" :key="index" @click="viewGeneration(gen.id)">
					<!-- 成功生成的图片 -->
					<view v-if="gen.status === 'SUCCESS' || gen.status === 'PARTIAL'" class="work-image-container">
						<!-- 使用新的preview_image_url字段显示首张预览图 -->
						<image v-if="gen.preview_image_url" 
							class="work-image" 
							:src="getImageUrl(gen.preview_image_url)" 
							mode="aspectFill"></image>
						<image v-else class="work-image" src="/static/placeholder.png" mode="aspectFill"></image>
						
						<!-- 如果是多图，显示图片数量标记 -->
						<view v-if="gen.images_count && gen.images_count > 1" class="image-count-badge">
							{{gen.images_count}}
						</view>
					</view>
					
					<!-- 失败状态 -->
					<view v-else-if="gen.status === 'FAILED'" class="work-image failed-image">
						<view class="failed-icon"></view>
						<view class="failed-text-container">
							<text class="failed-text">生成失败</text>
							<text class="refund-text">已返还</text>
						</view>
					</view>
					
					<!-- 默认状态（包括PENDING） -->
					<view v-else class="work-image-container">
						<image class="work-image" src="/static/placeholder.png" mode="aspectFill"></image>
						<view class="work-status" v-if="gen.status === 'PENDING' || gen.status === 'PROCESSING'">生成中...</view>
					</view>
					
					<text class="work-date">{{formatDate(gen.created_at)}}</text>
				</view>
			</view>
			
			<!-- 加载更多指示器 -->
			<view v-if="isLoadingWorks && myGenerations.length > 0" class="load-more-indicator">
				<view class="loading-spinner small"></view>
				<text>加载更多...</text>
			</view>
		</view>
	</scroll-view>
	<!-- 自定义签到成功提示 -->
	<view v-if="showCheckinSuccess" class="checkin-success-popup">
		<view class="checkin-success-content">
			<view class="success-icon"></view>
			<view class="success-title">签到成功</view>
			<view class="success-reward">+{{checkinReward}} 哇图币</view>
			<view class="success-tip">明天再来领取更多奖励~</view>
			
			<!-- 金币掉落动画元素 -->
			<view v-for="i in 8" :key="i" class="coin-animation" :class="`coin-${i}`"></view>
		</view>
	</view>
	
	<!-- 哇图币获取指南提示 -->
	<view v-if="showCoinsGuide" class="coins-guide-popup">
		<view class="coins-guide-content">
			<view class="guide-header">
				<view class="guide-title">获取更多哇图币</view>
				<view class="guide-close" @click="showCoinsGuide = false">×</view>
			</view>
			<view class="guide-item">
				<view class="guide-icon icon-sign"></view>
				<view class="guide-text">
					<view class="guide-item-title">每日签到</view>
					<view class="guide-item-desc">每天签到可获得20哇图币</view>
				</view>
			</view>
			<view class="guide-item">
				<view class="guide-icon icon-invite"></view>
				<view class="guide-text">
					<view class="guide-item-title">邀请好友</view>
					<view class="guide-item-desc">每邀请1位好友可获得50哇图币</view>
				</view>
			</view>
			<view class="guide-item">
				<view class="guide-icon icon-service"></view>
				<view class="guide-text">
					<view class="guide-item-title">参与活动</view>
					<view class="guide-item-desc">关注我们的活动获取更多奖励</view>
				</view>
			</view>
			<view class="guide-action" @click="signIn">
				<view v-if="!isSignedInToday" class="guide-button">立即签到</view>
				<view v-else class="guide-button disabled">今日已签到</view>
			</view>
		</view>
	</view>
	
	<!-- 使用充值弹窗组件 -->
	<RechargeModal 
		v-model="showRechargePopup" 
		:userInfo="userInfo"
		@recharge-success="onRechargeSuccess"></RechargeModal>
	
	<!-- 添加个人资料编辑弹窗 -->
	<ProfileEditModal 
		v-model="showProfileEditPopup" 
		:userInfo="userInfo" 
		@update-success="onProfileUpdateSuccess"></ProfileEditModal>
</template>

<script>
	import request from '../../utils/request.js'
	import { getImageUrl } from '../../utils/config.js'
	import RechargeModal from '../../components/RechargeModal.vue'
	import ProfileEditModal from '../../components/ProfileEditModal.vue'

	export default {
		components: {
			RechargeModal,
			ProfileEditModal
		},
		// 添加分享
		onShareAppMessage() {
			return {
				title: '人，猫生是旷野！！！',
				path: '/pages/profile/profile',
				imageUrl: '/static/share.jpg'
			};
		},
		onShareTimeline() {
			return {
				title: '人，猫生是旷野！！！',
				imageUrl: '/static/share.jpg'
			};
		},
		data() {
			return {
				quickTools: [
					{title: '每日签到', iconClass: 'icon-sign', action: 'signIn'},
					{title: '邀请有礼', iconClass: 'icon-invite', action: 'invite'},
					{title: '联系客服', iconClass: 'icon-service', action: 'service'},
					{title: '设置', iconClass: 'icon-setting', action: 'setting'}
				],
				myGenerations: [],
				userInfo: {},
				page: 1,
				pageSize: 8,
				hasMore: true,
				isLoadingWorks: false, // 控制作品加载状态
				isRefreshing: false, // 控制下拉刷新状态
				isFirstLoad: true, // 标记是否是首次加载
				isSignedInToday: false, // 标记用户今天是否已签到
				showCheckinSuccess: false, // 显示签到成功弹窗
				checkinReward: 20, // 签到奖励金额
				showCoinsGuide: false, // 是否显示获取哇图币指南
				showRechargePopup: false, // 控制充值弹窗显示
				showProfileEditPopup: false, // 控制个人资料编辑弹窗显示
			}
		},
		methods: {
			navigateToGenerate() {
				uni.switchTab({
					url: '/pages/index/index'
				});
			},
			loadUserInfo(force = false) {
				const app = getApp()
				if (!force && app.globalData.userInfo) {
					this.userInfo = app.globalData.userInfo
					this.checkSignedInToday(); // 加载用户信息时检查签到状态
					return
				}
				request.get('/wowpic/auth/me').then(res => {
					this.userInfo = res
					app.globalData.userInfo = res
					this.checkSignedInToday(); // 加载用户信息时检查签到状态
				}).catch(err => {
					console.error('获取用户信息失败', err);
					
					// 设置默认用户信息，确保界面不会空白
					if (!this.userInfo.id) {
						this.userInfo = {
							nickname: '用户',
							avatar_url: '/static/头像.png',
							coins: 0
						};
					}
					
					// 只在首次加载时显示提示，避免频繁弹窗
					if (this.isFirstLoad) {
						uni.showToast({
							title: '获取用户信息失败',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			/**
			 * 充值处理逻辑
			 * 点击头像右侧哇图币区域触发
			 * 修改为跳转到充值页面，解决iOS兼容性问题
			 */
			openRecharge() {
				console.log('用户点击充值按钮，跳转到充值页面');

				// 直接跳转到充值页面，不再区分系统
				uni.navigateTo({
					url: '/pages/pay/pay',
					success: () => {
						console.log('成功跳转到充值页面');
					},
					fail: (err) => {
						console.error('跳转充值页面失败:', err);
						uni.showToast({
							title: '页面跳转失败',
							icon: 'none'
						});
					}
				});
			},
			
			/**
			 * 充值成功回调
			 */
			onRechargeSuccess(amount) {
				// 充值成功后立即刷新一次用户信息
				this.loadUserInfo(true)
				// 微信支付回调到服务器存在几秒延迟，再次延迟刷新
				setTimeout(() => {
					this.loadUserInfo(true)
				}, 2000)
			},
			loadGenerations() {
				if (!this.hasMore || this.isLoadingWorks) return;
				
				// 设置加载状态为true
				this.isLoadingWorks = true;
				
				request.get('/wowpic/generate', {
					page: this.page,
					page_size: this.pageSize
				}).then(res => {
					if (res.items && res.items.length > 0) {
						this.myGenerations = [...this.myGenerations, ...res.items];
						this.page++;
						this.hasMore = res.items.length >= this.pageSize;
					} else {
						this.hasMore = false;
					}
					
					// 设置加载状态为false
					this.isLoadingWorks = false;
					
					// 如果是在刷新操作中，需要结束刷新状态
					if (this.isRefreshing) {
						this.isRefreshing = false;
					}
				}).catch(err => {
					console.error('加载生成历史失败', err);
					
					// 设置加载状态为false
					this.isLoadingWorks = false;
					
					// 如果是在刷新操作中，需要结束刷新状态
					if (this.isRefreshing) {
						this.isRefreshing = false;
					}
					
					// 显示加载失败提示
					uni.showToast({
						title: '加载作品失败，请下拉刷新重试',
						icon: 'none',
						duration: 2000
					});
					
					// 如果是首次加载失败，设置空数组确保显示无作品提示
					if (this.myGenerations.length === 0) {
						this.myGenerations = [];
					}
				});
			},
			viewGeneration(id) {
				uni.navigateTo({
					url: `/pages/generate/generate?generationId=${id}`
				});
			},
			formatDate(dateStr) {
				if (!dateStr) return '';
				const date = new Date(dateStr);
				return `${date.getMonth() + 1}月${date.getDate()}日`;
			},
			// 下拉刷新处理函数
			onRefresh() {
				console.log('下拉刷新触发');
				this.isRefreshing = true;
				
				// 重置页面和数据
				this.page = 1;
				this.myGenerations = [];
				this.hasMore = true;
				
				// 强制刷新用户信息（头像、昵称和哇图币）
				this.loadUserInfo(true);
				
				// 重新检查签到状态
				this.checkSignedInToday();
				
				// 重新加载作品列表
				this.loadGenerations();
			},
			onReachBottom() {
				if (this.hasMore) {
					this.loadGenerations();
				}
			},
			// 初始加载数据
			loadInitialData() {
				this.page = 1;
				this.myGenerations = [];
				this.hasMore = true;
				this.loadGenerations();
			},
			// 检查是否有新的生成作品
			checkForNewGenerations() {
				// 如果没有作品，直接加载
				if (this.myGenerations.length === 0) {
					this.loadInitialData();
					return;
				}
				
				// 只获取第一页，检查是否有新作品
				request.get('/wowpic/generate', {
					page: 1,
					page_size: this.pageSize
				}, { hideErrorTips: true }).then(res => {
					if (!res.items || res.items.length === 0) return;
					
					// 检查第一个作品ID是否与当前列表中的第一个相同
					if (this.myGenerations.length > 0 && 
						res.items[0].id !== this.myGenerations[0].id) {
						// 有新作品，重新加载
						this.loadInitialData();
					} else {
						// 检查是否有状态变化的作品（如从PENDING到SUCCESS）
						let needsUpdate = false;
						for (let i = 0; i < Math.min(res.items.length, this.myGenerations.length); i++) {
							if (res.items[i].id === this.myGenerations[i].id && 
								res.items[i].status !== this.myGenerations[i].status) {
								needsUpdate = true;
								break;
							}
						}
						
						if (needsUpdate) {
							this.loadInitialData();
						}
					}
				}).catch(err => {
					console.error('检查新作品失败', err);
				});
			},
			// 处理图片URL
			// 直接引用 utils/config.js 中的同名函数，避免冗余包装
			getImageUrl,
			// 加载更多作品
			loadMoreWorks() {
				console.log('滚动到底部，加载更多作品');
				if (this.hasMore && !this.isLoadingWorks) {
					this.loadGenerations();
				}
			},
			handleToolClick(action) {
				if (action === 'signIn') {
					if (this.isSignedInToday) {
						// 如果已经签到，只显示提示
						uni.showToast({
							title: '今天已经签到过啦~',
							icon: 'none'
						});
					} else {
						// 如果未签到，进行签到
						this.signIn();
					}
				} else if (action === 'invite') {
					uni.showToast({
						title: '邀请功能即将上线',
						icon: 'none'
					});
				} else if (action === 'service') {
					// 客服功能现在使用微信原生组件，此处不需要额外处理
					// 点击事件由button组件的open-type="contact"直接处理
				} else if (action === 'setting') {
					uni.showToast({
						title: '设置功能即将上线',
						icon: 'none'
					});
				}
			},
			async signIn() {
				try {
					const res = await request.post('/wowpic/checkin/daily');
					if (res.success) {
						// 更新用户哇图币数量
						const oldCoins = this.userInfo.coins || 0; // 保存原来的哇图币数量
						this.userInfo.coins = res.coins;
						
						// 显示自定义签到成功提示
						this.checkinReward = res.reward || 20; // 从响应中获取奖励金额，默认20
						this.showCheckinSuccess = true;
						
						// 3秒后隐藏提示
						setTimeout(() => {
							this.showCheckinSuccess = false;
						}, 3000);
						
						// 更新签到状态
						this.isSignedInToday = true;
						
						// 更新全局用户信息
						const app = getApp();
						app.globalData.userInfo = this.userInfo;
					} else {
						uni.showToast({
							title: res.message || '签到失败',
							icon: 'none'
						});
					}
				} catch (err) {
					console.error('签到失败', err);
					uni.showToast({
						title: '签到失败，请稍后再试',
						icon: 'none'
					});
				}
			},
			// 检查用户今天是否已签到
			checkSignedInToday() {
				const today = new Date();
				const year = today.getFullYear();
				const month = String(today.getMonth() + 1).padStart(2, '0');
				const day = String(today.getDate()).padStart(2, '0');
				const formattedDate = `${year}-${month}-${day}`;
				
				// 调用签到状态API
				request.get(`/wowpic/checkin/status`).then(res => {
					this.isSignedInToday = res.is_signed_in;
				}).catch(err => {
					console.error('检查签到状态失败', err);
					this.isSignedInToday = false; // 默认未签到
				});
			},
			
			/**
			 * 打开资料编辑弹窗
			 */
			openProfileEdit() {
				this.showProfileEditPopup = true;
			},
			
			/**
			 * 个人资料更新成功回调
			 */
			onProfileUpdateSuccess(updatedInfo) {
				// 更新本地用户信息
				this.userInfo = {...this.userInfo, ...updatedInfo};
				
				// 更新全局用户信息
				const app = getApp();
				if (app.globalData.userInfo) {
					app.globalData.userInfo = {...app.globalData.userInfo, ...updatedInfo};
				}
				
				// 显示成功提示
				uni.showToast({
					title: '个人资料已更新',
					icon: 'success'
				});
			},
			
			/**
			 * 复制用户ID
			 */
			copyUserId() {
				if (!this.userInfo.uuid) return;
				
				uni.setClipboardData({
					data: this.userInfo.uuid,
					success: () => {
						uni.showToast({
							title: 'ID已复制',
							icon: 'success',
							duration: 1500
						});
					}
				});
			},

		},
		created() {
			this.loadUserInfo();
		},
		onShow() {
			// 只在首次加载时获取作品列表
			if (this.isFirstLoad) {
				this.loadInitialData();
				this.isFirstLoad = false;
			} else {
				// 仅刷新用户信息（可能有哇图币变化）
				this.loadUserInfo();
				
				// 检查是否有新作品生成，如果有则更新
				this.checkForNewGenerations();
			}
			
			// 检查是否从生成页面跳转过来获取哇图币
			const pages = getCurrentPages();
			const prevPage = pages.length > 1 ? pages[pages.length - 2] : null;
			
			// 如果前一个页面是生成页面，并且用户哇图币较少（小于50），显示获取哇图币指南
			if (prevPage && prevPage.route && prevPage.route.includes('generate') && 
				this.userInfo.coins !== undefined && this.userInfo.coins < 50) {
				this.showCoinsGuide = true;
				// 3秒后自动隐藏
				setTimeout(() => {
					this.showCoinsGuide = false;
				}, 8000);
			}
		}
	}
</script>

<style>
	@import url('/static/styles/icons.css');
	
	.profile-container {
		height: 100vh;
		background-color: #F8F8F8;
		padding: 0 30rpx 0;
	}
	
	/* 移除点击时的蓝色高亮效果 */
	.avatar-container, .user-info, .coin-info, .tool-content, .empty-btn, .work-item {
		-webkit-tap-highlight-color: transparent;
	}
	
	/* 移动端禁用滚动条 */
	::-webkit-scrollbar {
		display: none !important;
		width: 0 !important;
		height: 0 !important;
	}
	
	/* 安全区域 */
	.safe-area {
		height: 88rpx; /* 根据机型可能需要调整 */
	}
	
	/* 用户信息卡片样式 */
	.user-card {
		display: flex;
		align-items: center;
		padding: 32rpx 30rpx; /* 减小了上下内边距，从40rpx减小到26rpx */
		background: linear-gradient(135deg, #4A90E2 0%, #7562FF 100%);
		border: 8rpx solid #333;
		border-radius: 24rpx;
		color: #FFFFFF;
		margin-bottom: 30rpx;
		box-shadow: 0 12rpx 24rpx rgba(0, 0, 0, 0.15);
		position: relative;
		overflow: hidden;
	}
	
	/* 卡通风格装饰元素 */
	.user-card::before {
		content: "";
		position: absolute;
		top: -10rpx;
		right: -10rpx;
		width: 100rpx;
		height: 100rpx;
		background: rgba(255, 255, 255, 0.2);
		border-radius: 50%;
		z-index: 0;
	}
	
	.avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		border: 6rpx solid #333;
		box-shadow: 0 0 0 4rpx #FFFFFF;
	}
	
	.user-info {
		flex: 1;
		margin-left: 20rpx;
		z-index: 1;
	}
	
	.username {
		font-size: 36rpx;
		font-weight: bold;
		display: block;
		margin-bottom: 6rpx;
		text-shadow: 2rpx 2rpx 0 rgba(0, 0, 0, 0.3);
	}
	
	.user-id {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.8);
		background-color: rgba(0, 0, 0, 0.2);
		padding: 4rpx 12rpx;
		border-radius: 20rpx;
		display: inline-block;
		border: 1rpx solid rgba(255, 255, 255, 0.3);
	}
	
	.coin-info {
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: rgba(78, 103, 235, 0.3);
		padding: 20rpx 24rpx;
		border-radius: 24rpx;
		border: 4rpx solid rgba(0, 0, 0, 0.25);
		z-index: 1;
		box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.2);
	}
	
	.coin-balance {
		display: flex;
		align-items: center;
		/* 删除了下边距 */
	}
	
	.coin-icon {
		width: 68rpx;
		height: 68rpx;
		margin-right: 12rpx;
	}
	
	.coin-amount {
		font-size: 46rpx;
		font-weight: bold;
		text-shadow: 0 2rpx 3rpx rgba(0, 0, 0, 0.15);
	}

	/* 充值按钮样式 */
	.recharge-btn {
		background-color: #7562FF;
		color: #FFFFFF;
		font-size: 28rpx;
		padding: 12rpx 30rpx;
		border-radius: 30rpx;
		border: 3rpx solid rgba(255, 255, 255, 0.6);
		font-weight: bold;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.25);
		position: relative;
		margin-top: 10rpx;
		letter-spacing: 2rpx;
	}
	

	
	/* 快捷功能区样式 */
	.quick-tools {
		display: flex;
		justify-content: space-between;
		background-color: #FFFFFF;
		border-radius: 24rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.05);
	}
	
	.tool-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 25%;
		transition: transform 0.3s ease;
	}
	
	.tool-item:active {
		transform: scale(0.95);
	}
	
	.tool-icon-wrapper {
		position: relative;
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 16rpx;
	}
	
	.tool-icon {
		width: 100%;
		height: 100%;
		display: block;
	}
	
	.notification-dot {
		position: absolute;
		top: -10rpx;
		right: -10rpx;
		width: 24rpx;
		height: 24rpx;
		background-color: #FF4D4F; /* 红色提醒 */
		border-radius: 50%;
		border: 2rpx solid #FFFFFF;
		box-shadow: 0 2rpx 6rpx rgba(255, 77, 79, 0.6);
		z-index: 1;
	}
	
	.tool-text-container {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.tool-text {
		font-size: 24rpx;
		color: #333;
	}
	
	.signed-tag {
		font-size: 22rpx;
		color: #52C41A; /* 绿色，表示已完成 */
		background-color: #F6FFED; /* 浅绿色背景 */
		padding: 4rpx 12rpx;
		border-radius: 30rpx;
		border: 1rpx solid #B7EB8F;
		display: flex;
		align-items: center;
	}
	
	.signed-tag::before {
		content: '';
		display: inline-block;
		width: 12rpx;
		height: 12rpx;
		border-radius: 50%;
		background-color: #52C41A;
		margin-right: 6rpx;
	}

	/* 签到成功提示样式 */
	.checkin-success-popup {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 999;
	}
	
	.checkin-success-content {
		background-color: #FFFFFF;
		border-radius: 24rpx;
		padding: 50rpx 60rpx;
		text-align: center;
		box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15);
		animation: popIn 0.5s ease;
		max-width: 70%;
		position: relative;
		overflow: hidden;
	}
	
	.checkin-success-content::before {
		content: "";
		position: absolute;
		top: -30rpx;
		left: -30rpx;
		width: 150rpx;
		height: 150rpx;
		background: linear-gradient(135deg, rgba(255, 149, 0, 0.2) 0%, rgba(255, 149, 0, 0) 70%);
		border-radius: 50%;
		z-index: 0;
	}
	
	.checkin-success-content::after {
		content: "";
		position: absolute;
		bottom: -30rpx;
		right: -30rpx;
		width: 150rpx;
		height: 150rpx;
		background: linear-gradient(135deg, rgba(82, 196, 26, 0.2) 0%, rgba(82, 196, 26, 0) 70%);
		border-radius: 50%;
		z-index: 0;
	}
	
	@keyframes popIn {
		0% {
			transform: scale(0.8);
			opacity: 0;
		}
		100% {
			transform: scale(1);
			opacity: 1;
		}
	}
	
	.success-icon {
		width: 120rpx;
		height: 120rpx;
		margin: 0 auto 30rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2352C41A'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
		position: relative;
		z-index: 1;
	}
	
	.success-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
		position: relative;
		z-index: 1;
	}
	
	.success-reward {
		font-size: 48rpx;
		font-weight: bold;
		color: #FF9500;
		margin-bottom: 20rpx;
		text-shadow: 0 2rpx 4rpx rgba(255, 149, 0, 0.2);
		position: relative;
		z-index: 1;
		animation: bounceIn 0.8s ease 0.2s both;
	}
	
	@keyframes bounceIn {
		0% {
			transform: scale(0.3);
			opacity: 0;
		}
		50% {
			transform: scale(1.2);
		}
		70% {
			transform: scale(0.9);
		}
		100% {
			transform: scale(1);
			opacity: 1;
		}
	}
	
	.success-tip {
		font-size: 28rpx;
		color: #999;
		position: relative;
		z-index: 1;
	}
	
	/* 金币动画效果 */
	.coin-animation {
		position: absolute;
		width: 40rpx;
		height: 40rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FFD700'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.31-8.86c-1.77-.45-2.34-.94-2.34-1.67 0-.84.79-1.43 2.1-1.43 1.38 0 1.9.66 1.94 1.64h1.71c-.05-1.34-.87-2.57-2.49-2.97V5H10.9v1.69c-1.51.32-2.72 1.3-2.72 2.81 0 1.79 1.49 2.69 3.66 3.21 1.95.46 2.34 1.15 2.34 1.87 0 .53-.39 1.39-2.1 1.39-1.6 0-2.23-.72-2.32-1.64H8.04c.1 1.7 1.36 2.66 2.86 2.97V19h2.34v-1.67c1.52-.29 2.72-1.16 2.73-2.77-.01-2.2-1.9-2.96-3.66-3.42z'/%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
		z-index: 2;
		opacity: 0;
	}
	
	.coin-1 {
		top: 30%;
		left: 10%;
		animation: coinFall 1.5s ease-in 0.1s forwards;
	}
	
	.coin-2 {
		top: 20%;
		left: 30%;
		animation: coinFall 1.5s ease-in 0.3s forwards;
	}
	
	.coin-3 {
		top: 10%;
		left: 50%;
		animation: coinFall 1.5s ease-in 0.5s forwards;
	}
	
	.coin-4 {
		top: 15%;
		left: 70%;
		animation: coinFall 1.5s ease-in 0.2s forwards;
	}
	
	.coin-5 {
		top: 25%;
		right: 15%;
		animation: coinFall 1.5s ease-in 0.4s forwards;
	}
	
	.coin-6 {
		top: 10%;
		right: 40%;
		animation: coinFall 1.5s ease-in 0.6s forwards;
	}
	
	.coin-7 {
		top: 5%;
		left: 25%;
		animation: coinFall 1.5s ease-in 0.7s forwards;
	}
	
	.coin-8 {
		top: 15%;
		right: 25%;
		animation: coinFall 1.5s ease-in 0.2s forwards;
	}
	
	@keyframes coinFall {
		0% {
			transform: translateY(-100rpx) rotate(0deg);
			opacity: 1;
		}
		50% {
			opacity: 1;
		}
		100% {
			transform: translateY(300rpx) rotate(360deg);
			opacity: 0;
		}
	}
	
	/* 哇图币获取指南样式 */
	.coins-guide-popup {
		position: fixed;
		bottom: 30rpx;
		left: 30rpx;
		right: 30rpx;
		z-index: 998;
		animation: slideUp 0.5s ease;
	}
	
	@keyframes slideUp {
		0% {
			transform: translateY(100%);
			opacity: 0;
		}
		100% {
			transform: translateY(0);
			opacity: 1;
		}
	}
	
	.coins-guide-content {
		background-color: #FFFFFF;
		border-radius: 20rpx;
		padding: 30rpx;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
		border: 4rpx solid #7562FF;
	}
	
	.guide-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.guide-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}
	
	.guide-close {
		width: 44rpx;
		height: 44rpx;
		line-height: 40rpx;
		text-align: center;
		font-size: 36rpx;
		color: #999;
		border-radius: 50%;
		background-color: #F0F0F0;
	}
	
	.guide-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 2rpx solid #F0F0F0;
	}
	
	.guide-icon {
		width: 60rpx;
		height: 60rpx;
		margin-right: 20rpx;
	}
	
	.guide-text {
		flex: 1;
	}
	
	.guide-item-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 6rpx;
	}
	
	.guide-item-desc {
		font-size: 24rpx;
		color: #666;
	}
	
	.guide-action {
		margin-top: 20rpx;
		display: flex;
		justify-content: center;
	}
	
	.guide-button {
		background: linear-gradient(135deg, #4A90E2 0%, #7562FF 100%);
		color: #FFFFFF;
		font-size: 28rpx;
		padding: 16rpx 60rpx;
		border-radius: 40rpx;
		box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.1);
	}
	
	.guide-button.disabled {
		background: #CCCCCC;
		color: #FFFFFF;
	}
	
	/* 作品区域样式 */
	.works-section {
		background-color: #FFFFFF;
		border-radius: 24rpx;
		padding: 30rpx;
		box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.05);
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 30rpx;
	}
	
	.works-grid {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	}
	
	/* 无作品状态 */
	.no-works {
		text-align: center;
		padding: 60rpx 0;
	}
	
	.no-works text {
		font-size: 28rpx;
		color: #999;
		display: block;
		margin-bottom: 30rpx;
	}
	
	.empty-btn {
		display: inline-block;
		background: linear-gradient(135deg, #6291E7 0%, #4F7BD1 100%); /* 蓝色渐变，与logo中的蓝色元素匹配 */
		color: #FFF;
		padding: 16rpx 40rpx;
		border-radius: 40rpx;
		font-size: 28rpx;
		box-shadow: 0 6rpx 12rpx rgba(98, 145, 231, 0.3);
		border: 2rpx solid rgba(255, 255, 255, 0.5);
	}
	
	/* 加载状态样式 */
	.loading-works {
		padding: 60rpx 0;
		text-align: center;
	}
	
	.loading-indicator {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}
	
	.loading-text {
		font-size: 28rpx;
		color: #999;
		margin-top: 20rpx;
	}
	
	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f3f3f3;
		border-top: 4rpx solid #7562FF;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}
	
	.loading-spinner.small {
		width: 30rpx;
		height: 30rpx;
	}
	
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
	
	.load-more-indicator {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20rpx 0;
		color: #999;
		font-size: 24rpx;
	}
	
	.load-more-indicator .loading-spinner {
		margin-right: 10rpx;
	}
	
	/* 作品项样式 */
	.work-item {
		position: relative;
		width: 48%;
		margin-bottom: 20rpx;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
		background-color: #f0f0f0;
	}
	
	.work-image-container {
		position: relative;
		width: 100%;
		height: 300rpx;
		border-radius: 16rpx;
		overflow: hidden;
	}
	
	.work-image {
		width: 100%;
		height: 100%;
		display: block;
		border-radius: 16rpx;
	}
	
	.image-count-badge {
		position: absolute;
		top: 10rpx;
		right: 10rpx;
		background-color: rgba(0, 0, 0, 0.6);
		color: #FFF;
		font-size: 24rpx;
		padding: 4rpx 12rpx;
		border-radius: 10rpx;
		z-index: 1;
	}
	
	.work-status {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		color: #FFF;
		font-size: 28rpx;
		border-radius: 16rpx;
	}
	
	.work-date {
		position: absolute;
		bottom: 10rpx;
		right: 10rpx;
		background-color: rgba(0, 0, 0, 0.6);
		color: #FFF;
		font-size: 20rpx;
		padding: 4rpx 12rpx;
		border-radius: 10rpx;
	}
	
	/* 失败状态样式 */
	.failed-image {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		background-color: rgba(255, 192, 203, 0.8);
		border-radius: 16rpx;
		overflow: hidden;
		height: 300rpx;
	}
	
	.failed-icon {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 20rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FFFFFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='15' y1='9' x2='9' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='9' x2='15' y2='15'%3E%3C/line%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}
	
	.failed-text-container {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.failed-text {
		color: #FFFFFF;
		font-size: 28rpx;
		font-weight: bold;
		margin-bottom: 8rpx;
	}
	
	.refund-text {
		color: #FFFFFF;
		font-size: 24rpx;
	}
	
	/* 客服按钮样式 */
	.service-button {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 100%;
		height: 100%;
		background-color: transparent;
		padding: 0;
		margin: 0;
		line-height: normal;
	}
	
	.service-button::after {
		border: none;
	}
	
	.service-tool {
		position: relative;
	}
	
	/* 用于客服按钮中的工具内容，确保与其他按钮样式统一 */
	.tool-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 100%;
	}

	/* 充值弹窗样式已移至组件中 */

	/* 充值成功动画相关样式已移至组件中 */
	
	/* 添加头像编辑图标样式 */
	.avatar-container {
		position: relative;
		cursor: pointer;
	}
	
	.avatar-edit-icon {
		position: absolute;
		right: -5rpx;
		bottom: -5rpx;
		width: 40rpx;
		height: 40rpx;
		background-color: #7562FF;
		border-radius: 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
		z-index: 1;
		border: 2rpx solid #FFFFFF;
	}
	
	.icon-edit {
		width: 24rpx;
		height: 24rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FFFFFF'%3E%3Cpath d='M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z'/%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}
	
	
	/* 用户信息区域添加指针样式 */
	.user-info {
		cursor: pointer;
	}
</style>
